.data-management-layout {
  height: 100%;
  
  .region-selector-card {
    height: 100%;
    
    .ant-card-body {
      height: calc(100% - 57px);
      overflow-y: auto;
    }
  }
  
  .content-card {
    height: 100%;
    
    .ant-card-body {
      height: calc(100% - 57px);
      overflow-y: auto;
    }
  }
}

.region-tree-selector {
  .region-tree-node {
    padding: 4px 8px;
    margin: 2px 0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    
    &:hover {
      background-color: #f5f5f5;
    }
    
    &.selected {
      background-color: #e6f7ff;
      color: #1890ff;
      font-weight: 500;
    }
    
    &.all-regions {
      font-weight: 500;
      color: #666;
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 8px;
      padding-bottom: 8px;
    }
  }
  
  .region-tree-children {
    margin-left: 16px;
    border-left: 1px dashed #d9d9d9;
    padding-left: 8px;
  }
  
  .region-tree-icon {
    margin-right: 6px;
    color: #999;
    
    &.expanded {
      transform: rotate(90deg);
    }
  }
}
