# DataManagementLayout 数据管理布局组件

## 概述

DataManagementLayout 是一个通用的数据管理页面布局组件，提供了左侧区域树形选择器和右侧内容区域的标准布局。该组件专为历史要素管理、山塬管理、水系管理等需要按区域筛选数据的页面设计。

## 功能特性

### 🌳 左侧区域选择器
- 树形显示区域层级结构
- 支持点击选择区域
- 支持取消选择（显示全部数据）
- 自动展开第一级区域
- 响应式布局适配

### 📊 右侧内容区域
- 灵活的内容区域，可放置任何组件
- 自适应高度和宽度
- 支持自定义标题

### 🎨 布局特性
- 响应式栅格布局
- 可配置左右区域宽度比例
- 可选择是否显示区域选择器
- 统一的卡片样式

## 使用方法

### 基本用法

```tsx
import DataManagementLayout from '@/components/DataManagementLayout';

const MyDataPage: React.FC = () => {
  const [selectedRegionId, setSelectedRegionId] = useState<number>();

  const handleRegionChange = (regionId?: number) => {
    setSelectedRegionId(regionId);
    // 根据选中的区域重新获取数据
    fetchData(regionId);
  };

  return (
    <DataManagementLayout
      selectedRegionId={selectedRegionId}
      onRegionChange={handleRegionChange}
      selectorTitle="区域选择"
      style={{ height: 'calc(100vh - 200px)' }}
    >
      {/* 右侧内容区域 */}
      <div>
        <SearchToolbar />
        <DataTable />
      </div>
    </DataManagementLayout>
  );
};
```

### 高级配置

```tsx
<DataManagementLayout
  selectorSpan={8}           // 左侧区域选择器宽度
  contentSpan={16}           // 右侧内容区域宽度
  selectedRegionId={regionId}
  onRegionChange={handleRegionChange}
  showRegionSelector={true}  // 是否显示区域选择器
  selectorTitle="选择区域"   // 区域选择器标题
  contentTitle="数据列表"    // 内容区域标题
  className="custom-layout"  // 自定义样式类
  style={{ height: '600px' }} // 自定义样式
>
  <YourContent />
</DataManagementLayout>
```

## API 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| selectorSpan | number | 6 | 左侧区域选择器的栅格宽度 |
| contentSpan | number | 18 | 右侧内容区域的栅格宽度 |
| selectedRegionId | number | undefined | 当前选中的区域ID |
| onRegionChange | (regionId?: number) => void | undefined | 区域选择变化回调 |
| showRegionSelector | boolean | true | 是否显示区域选择器 |
| selectorTitle | string | '区域选择' | 区域选择器标题 |
| contentTitle | string | undefined | 内容区域标题 |
| className | string | undefined | 自定义样式类名 |
| style | React.CSSProperties | undefined | 自定义样式 |
| children | React.ReactNode | - | 右侧内容区域 |

## RegionTreeSelector 区域树形选择器

### 功能特性
- 基于字典数据自动构建区域树
- 支持多级区域层级显示
- 点击展开/收起子区域
- 高亮显示当前选中区域
- "全部区域"选项支持取消筛选

### 使用示例

```tsx
import { RegionTreeSelector } from '@/components/DataManagementLayout/RegionTreeSelector';

<RegionTreeSelector
  selectedRegionId={selectedId}
  onRegionChange={handleRegionChange}
/>
```

## 样式定制

组件提供了完整的CSS类名，可以通过覆盖样式进行定制：

```less
.data-management-layout {
  // 整体布局样式
  
  .region-selector-card {
    // 左侧区域选择器卡片样式
  }
  
  .content-card {
    // 右侧内容区域卡片样式
  }
}

.region-tree-selector {
  .region-tree-node {
    // 区域节点样式
    
    &.selected {
      // 选中状态样式
    }
    
    &.all-regions {
      // "全部区域"节点样式
    }
  }
}
```

## 最佳实践

1. **高度设置**: 建议为组件设置合适的高度，如 `calc(100vh - 200px)`
2. **数据联动**: 在 `onRegionChange` 回调中及时更新数据
3. **加载状态**: 在数据获取过程中显示加载状态
4. **错误处理**: 处理区域数据加载失败的情况
5. **响应式**: 在小屏幕设备上考虑隐藏区域选择器

## 注意事项

- 组件依赖于全局的字典状态管理（dva model）
- 需要确保区域字典数据已正确加载
- 区域ID为undefined时表示选择"全部区域"
- 组件会自动处理区域数据的加载和缓存
