import DataManagementLayout from '@/components/DataManagementLayout';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Space, Tabs, Typography } from 'antd';
import React from 'react';
import { BatchImportModal } from './components/BatchImportModal';
import { BatchOperationBar } from './components/BatchOperationBar';
import { HistoricalElementForm } from './components/HistoricalElementForm';
import { HistoricalElementStatistics } from './components/HistoricalElementStatistics';
import { HistoricalElementTable } from './components/HistoricalElementTable';
import { HistoricalElementTimeline } from './components/HistoricalElementTimeline';
import { HistoricalElementToolbar } from './components/HistoricalElementToolbar';
import { useHistoricalElementManager } from './hooks/useHistoricalElementManager';

const { Title } = Typography;

const AdminHistoricalElement: React.FC = () => {
  const manager = useHistoricalElementManager();

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题和操作按钮 */}
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 24,
        }}
      >
        <Title level={2}>历史要素管理</Title>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={manager.handleAdd}
          >
            添加历史要素
          </Button>
          <Button onClick={manager.handleImportModalOpen}>批量导入</Button>
        </Space>
      </div>

      {/* 标签页内容 */}
      <Tabs
        activeKey={manager.activeTab}
        onChange={manager.handleTabChange}
        items={[
          {
            key: 'list',
            label: '数据列表',
            children: (
              <DataManagementLayout
                selectedRegionId={manager.regionFilter}
                onRegionChange={manager.handleRegionFilterChange}
                selectorTitle="区域选择"
                style={{ height: 'calc(100vh - 200px)' }}
              >
                <div>
                  {/* 搜索和筛选工具栏 */}
                  <HistoricalElementToolbar
                    searchKeyword={manager.searchKeyword}
                    regionFilter={manager.regionFilter}
                    typeFilter={manager.typeFilter}
                    onSearchKeywordChange={(value) => manager.handleSearch(value)}
                    onSearch={manager.handleSearch}
                    onRegionFilterChange={manager.handleRegionFilterChange}
                    onTypeFilterChange={manager.handleTypeFilterChange}
                    onRefresh={manager.handleRefresh}
                    onReset={manager.handleReset}
                    showRegionFilter={false}
                  />

                  {/* 批量操作栏 */}
                  <BatchOperationBar
                    selectedCount={manager.selectedRowKeys.length}
                    batchLoading={manager.batchLoading}
                    onClearSelection={manager.clearSelection}
                    onBatchDelete={manager.handleBatchDelete}
                  />

                  {/* 数据表格 */}
                  <HistoricalElementTable
                    data={manager.data}
                    loading={manager.loading}
                    selectedRowKeys={manager.selectedRowKeys}
                    pagination={manager.pagination}
                    onSelectChange={manager.handleSelectChange}
                    onEdit={manager.handleEdit}
                    onDelete={manager.handleDelete}
                    onPaginationChange={manager.handlePaginationChange}
                    onShowSizeChange={manager.handleShowSizeChange}
                  />
                </div>
              </DataManagementLayout>
            ),
          },
          {
            key: 'statistics',
            label: '统计分析',
            children: (
              <HistoricalElementStatistics
                statistics={manager.statistics}
                loading={manager.statisticsLoading}
              />
            ),
          },
          {
            key: 'timeline',
            label: '时间轴',
            children: (
              <HistoricalElementTimeline
                timelineData={manager.timelineData}
                loading={manager.timelineLoading}
              />
            ),
          },
        ]}
      />

      {/* 添加/编辑表单模态框 */}
      <HistoricalElementForm
        visible={manager.modalVisible}
        loading={manager.operationLoading}
        editingItem={manager.editingItem}
        onOk={manager.handleSubmit}
        onCancel={manager.handleModalCancel}
      />

      {/* 批量导入模态框 */}
      <BatchImportModal
        visible={manager.importModalVisible}
        loading={manager.batchLoading}
        onOk={manager.handleBatchImport}
        onCancel={manager.handleImportModalClose}
      />
    </div>
  );
};

export default AdminHistoricalElement;
