/**
 * @file 区域树形选择器组件
 * @description 用于显示和选择区域的树形组件
 * <AUTHOR> Assistant
 * @date 2025-08-30
 */
import { DictionaryState } from '@/models/dictionary';
import { CaretRightOutlined, GlobalOutlined } from '@ant-design/icons';
import { connect, useDispatch } from '@umijs/max';
import { Empty, Spin } from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

export interface RegionTreeNode {
  id: number;
  name: string;
  code?: string;
  parentId?: number;
  children?: RegionTreeNode[];
  level?: number;
}

export interface RegionTreeSelectorProps {
  /** 当前选中的区域ID */
  selectedRegionId?: number;
  /** 区域选择变化回调 */
  onRegionChange?: (regionId?: number) => void;
  /** 字典状态 */
  dictionary?: DictionaryState;
  /** 自定义样式类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
}

interface TreeNodeProps {
  node: RegionTreeNode;
  selectedId?: number;
  expandedKeys: Set<number>;
  onSelect: (id?: number) => void;
  onToggleExpand: (id: number) => void;
  level: number;
}

const TreeNode: React.FC<TreeNodeProps> = ({
  node,
  selectedId,
  expandedKeys,
  onSelect,
  onToggleExpand,
  level,
}) => {
  const hasChildren = node.children && node.children.length > 0;
  const isExpanded = expandedKeys.has(node.id);
  const isSelected = selectedId === node.id;

  return (
    <div>
      <div
        className={`region-tree-node ${isSelected ? 'selected' : ''}`}
        style={{ paddingLeft: level * 16 }}
        onClick={() => onSelect(node.id)}
      >
        {hasChildren && (
          <CaretRightOutlined
            className={`region-tree-icon ${isExpanded ? 'expanded' : ''}`}
            onClick={(e) => {
              e.stopPropagation();
              onToggleExpand(node.id);
            }}
          />
        )}
        {!hasChildren && <span style={{ width: 14, display: 'inline-block' }} />}
        <span>{node.name}</span>
      </div>
      {hasChildren && isExpanded && (
        <div>
          {node.children!.map((child) => (
            <TreeNode
              key={child.id}
              node={child}
              selectedId={selectedId}
              expandedKeys={expandedKeys}
              onSelect={onSelect}
              onToggleExpand={onToggleExpand}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  );
};

const RegionTreeSelector: React.FC<RegionTreeSelectorProps> = ({
  selectedRegionId,
  onRegionChange,
  dictionary,
  className,
  style,
}) => {
  const dispatch = useDispatch();
  const [expandedKeys, setExpandedKeys] = useState<Set<number>>(new Set());
  const [loading, setLoading] = useState(false);

  // 加载区域字典数据
  useEffect(() => {
    if (!dictionary?.region?.data?.length) {
      setLoading(true);
      dispatch({
        type: 'dictionary/fetchRegionDict',
      }).finally(() => {
        setLoading(false);
      });
    }
  }, [dispatch, dictionary?.region?.data]);

  // 构建树形数据
  const treeData = useMemo(() => {
    if (!dictionary?.region?.data?.length) return [];

    const regionMap = new Map<number, RegionTreeNode>();
    const rootNodes: RegionTreeNode[] = [];

    // 先创建所有节点
    dictionary.region.data.forEach((item) => {
      regionMap.set(item.id, {
        id: item.id,
        name: item.region_name,
        code: item.region_code,
        parentId: item.parent_id || undefined,
        children: [],
        level: item.level,
      });
    });

    // 构建树形结构
    regionMap.forEach((node) => {
      if (node.parentId) {
        const parent = regionMap.get(node.parentId);
        if (parent) {
          parent.children = parent.children || [];
          parent.children.push(node);
        }
      } else {
        rootNodes.push(node);
      }
    });

    // 排序
    const sortNodes = (nodes: RegionTreeNode[]) => {
      nodes.sort((a, b) => a.name.localeCompare(b.name));
      nodes.forEach((node) => {
        if (node.children?.length) {
          sortNodes(node.children);
        }
      });
    };

    sortNodes(rootNodes);
    return rootNodes;
  }, [dictionary?.region?.data]);

  // 处理节点选择
  const handleSelect = useCallback(
    (regionId?: number) => {
      onRegionChange?.(regionId);
    },
    [onRegionChange],
  );

  // 处理展开/收起
  const handleToggleExpand = useCallback((nodeId: number) => {
    setExpandedKeys((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  }, []);

  // 默认展开第一级节点
  useEffect(() => {
    if (treeData.length > 0 && expandedKeys.size === 0) {
      const firstLevelIds = treeData.map((node) => node.id);
      setExpandedKeys(new Set(firstLevelIds));
    }
  }, [treeData, expandedKeys.size]);

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '20px' }}>
        <Spin />
      </div>
    );
  }

  if (!treeData.length) {
    return <Empty description="暂无区域数据" />;
  }

  return (
    <div className={`region-tree-selector ${className || ''}`} style={style}>
      {/* 全部区域选项 */}
      <div
        className={`region-tree-node all-regions ${
          !selectedRegionId ? 'selected' : ''
        }`}
        onClick={() => handleSelect(undefined)}
      >
        <GlobalOutlined style={{ marginRight: 6 }} />
        全部区域
      </div>

      {/* 区域树 */}
      {treeData.map((node) => (
        <TreeNode
          key={node.id}
          node={node}
          selectedId={selectedRegionId}
          expandedKeys={expandedKeys}
          onSelect={handleSelect}
          onToggleExpand={handleToggleExpand}
          level={0}
        />
      ))}
    </div>
  );
};

export default connect(({ dictionary }: { dictionary: DictionaryState }) => ({
  dictionary,
}))(RegionTreeSelector);
